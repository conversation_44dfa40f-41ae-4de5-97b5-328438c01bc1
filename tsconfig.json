{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "types": ["node"]}, "include": ["src/**/*", "tests/**/*", "wdio.conf.ts"], "exclude": ["node_modules", "dist"], "ts-node": {"transpileOnly": true, "files": true, "compilerOptions": {"module": "commonjs"}}}