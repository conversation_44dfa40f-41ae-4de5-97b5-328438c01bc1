{"name": "websocket_ai_mobile_single_test", "version": "1.0.0", "description": "websocket_ai_mobile_single_test port = 3025", "main": "index.js", "scripts": {"test": "wdio run wdio.conf.ts", "dev": "nodemon --exec 'ts-node src/server.ts'", "start": "ts-node src/server.ts", "build": "tsc"}, "nodemonConfig": {"ignore": ["client-apps/**", "test-results/**", "downloads/**", "logs/**", "dist/**", "node_modules/**", "screenshots/**", "tests/**", "*.log", "*.tmp"], "ext": "ts,js,json"}, "repository": {"type": "git", "url": "git+https://github.com/agentq-ai/websocket_ai_mobile_single_test.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/agentq-ai/websocket_ai_mobile_single_test/issues"}, "homepage": "https://github.com/agentq-ai/websocket_ai_mobile_single_test#readme", "dependencies": {"@bull-board/api": "^6.10.1", "@bull-board/express": "^6.10.1", "@google-cloud/storage": "^7.16.0", "@google/generative-ai": "^0.24.0", "@wdio/cli": "^9.19.1", "@wdio/junit-reporter": "^9.19.1", "@wdio/local-runner": "^9.19.1", "@wdio/mocha-framework": "^9.19.1", "@wdio/spec-reporter": "^9.19.1", "agentq_mobile_automation_test": "^1.0.7", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "bullmq": "^5.53.3", "commander": "^13.0.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "form-data": "^4.0.3", "glob": "^11.0.2", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "luxon": "^3.5.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "openai": "^4.20.1", "ts-node": "^10.9.1", "typescript": "^5.2.2", "webdriverio": "^9.19.1", "ws": "^8.18.3", "wdio-video-reporter": "^6.2.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/ioredis": "^4.28.10", "@types/jsonwebtoken": "^9.0.5", "@types/luxon": "^3.4.2", "@types/mocha": "^10.0.10", "@types/multer": "^1.4.12", "@types/node": "^20.19.10", "@types/webdriverio": "^4.13.3", "@types/ws": "^8.5.10", "@wdio/appium-service": "^9.19.2", "@wdio/globals": "^9.17.0", "concurrently": "^8.2.2", "nodemon": "^3.0.1"}}